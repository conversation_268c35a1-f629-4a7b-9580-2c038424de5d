package com.facishare.crm.fmcg.tpm.web.manager;

import com.beust.jcommander.internal.Sets;
import com.facishare.appserver.checkins.api.model.GetTPMConfig;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.LanguageReplaceWrapper;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.PaasReferenceService;
import com.facishare.crm.fmcg.tpm.session.SessionSendService;
import com.facishare.crm.fmcg.tpm.session.model.SessionContentSyncErrorActivity;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeTemplateVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityType;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.GetConfigValueByKey;
import com.fmcg.framework.http.contract.reference.PaasReferenceBatchDelete;
import com.fmcg.framework.http.contract.reference.PaasReferenceCommonArg;
import com.fmcg.framework.http.contract.reference.PaasReferenceCreate;
import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 15:16
 */
//IgnoreI18nFile
@Component("activityTypeManager")
@Slf4j
@SuppressWarnings("Duplicates")
public class ActivityTypeManager implements IActivityTypeManager {

    @Resource
    private ActivityTypeDAO activityTypeDAO;

    @Resource
    private ActivityNodeTemplateDAO activityNodeTemplateDAO;

    @Resource(name = "describeLogicService")
    private DescribeLogicService describeService;

    @Resource
    private ObjectDataProxy objectDataProxy;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ActivityNodeTemplateManager activityNodeTemplateManager;

    @Resource
    private SessionSendService sessionSendService;

    @Resource
    private ShopMMService shopMMService;

    @Resource
    private PaasReferenceService paasReferenceService;

    @Resource
    private DescribeLogicService describeLogicService;

    @Resource
    private IObjectDescribeService objectDescribeService;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private TPM2Service tpm2Service;

    protected static final Set<String> SYNC_ACTIVITY_TYPE_OBJECT_API_NAMES = Sets.newHashSet();

    static {
        SYNC_ACTIVITY_TYPE_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_OBJ);
        SYNC_ACTIVITY_TYPE_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        SYNC_ACTIVITY_TYPE_OBJECT_API_NAMES.add(ApiNames.TPM_DEALER_ACTIVITY_COST);
    }

    private static final String CUSTOMER_ACCOUNT_APP = "customer_account_app";

    private static final String DISPLAY_ACTIVITY_TYPE_UNIQUE_ID = "6498fc7249a39d0001c748e8";
    private static final String LIVE_ACTIVITY_TYPE_UNIQUE_ID = "6498fc7249a39d0001c748e9";
    private static final String ACTIVITY_TYPE_DELETED_IDENTIFY = "(已删除)"; //ignorei18n

    @Override
    public boolean deleteAble(String tenantId, String id) {
        ActivityTypePO activityTypePO = activityTypeDAO.get(tenantId, id);
        if (Objects.isNull(activityTypePO)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_MANAGER_0));
        }
        if (!StatusType.INVALID.value().equals(activityTypePO.getStatus())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_MANAGER_1));
        }
        List<IObjectData> activityByActivityTypeIds = findActivityByActivityTypeId(tenantId, id);
        if (CollectionUtils.notEmpty(activityByActivityTypeIds)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_MANAGER_2));
        }

        List<IObjectData> unifiedCaseByActivityTypeId = findActivityUnifiedCaseByActivityTypeId(tenantId, id);
        if (CollectionUtils.notEmpty(unifiedCaseByActivityTypeId)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_MANAGER_3));
        }

        return true;
    }

    @Override
    public ActivityTypeExt find(String tenantId, String activityTypeId) {
        return ActivityTypeExt.of(activityTypeDAO.get(tenantId, activityTypeId));
    }

    @Override
    public void update(String tenantId, Integer employeeId, String activityTypeId, ActivityTypePO data) {
        activityTypeDAO.edit(tenantId, employeeId, activityTypeId, data);
    }

    @Override
    public List<ActivityTypeExt> findByActivityTypeIds(String tenantId, List<String> activityTypeIds) {
        return activityTypeDAO.query(tenantId, activityTypeIds).stream().map(ActivityTypeExt::of).collect(Collectors.toList());
    }

    @Override
    public ActivityTypeExt findByActivityId(String tenantId, String activityId) {
        return ActivityTypeExt.of(findActivityTypeByActivityId(tenantId, activityId));
    }

    @Override
    public ActivityTypeExt findByActivityUnifiedCaseId(String tenantId, String activityUnifiedCaseId) {
        return ActivityTypeExt.of(findActivityTypeByActivityUnifiedCaseId(tenantId, activityUnifiedCaseId));
    }

    @Override
    public void basicInformationValidation(String tenantId, IActivityType activityType) {
        nameValidation(activityType.getName());
        duplicateNameValidation(tenantId, activityType.getName());
        apiNameValidation(activityType.getApiName());
        duplicateApiNameValidation(tenantId, activityType.getApiName());
        descriptionValidation(activityType.getDescription());
    }


    @Override
    public void basicInformationValidation(String tenantId, String id, IActivityType activityType) {
        nameValidation(activityType.getName());
        duplicateNameValidation(tenantId, id, activityType.getName());
        apiNameValidation(activityType.getApiName());
        duplicateApiNameValidation(tenantId, id, activityType.getName());
        descriptionValidation(activityType.getDescription());
    }

    @Override
    public void duplicateNameValidation(String tenantId, String name) {
        if (activityTypeDAO.isDuplicateName(tenantId, name)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_NAME_DUPLICATE_ERROR));
        }
    }

    @Override
    public void duplicateNameValidation(String tenantId, String id, String name) {
        if (activityTypeDAO.isDuplicateName(tenantId, id, name)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_NAME_DUPLICATE_ERROR));
        }
    }

    @Override
    public void duplicateApiNameValidation(String tenantId, String name) {
        if (activityTypeDAO.isDuplicateApiName(tenantId, name)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE__API_NAME_DUPLICATE_ERROR));
        }
    }

    @Override
    public void duplicateApiNameValidation(String tenantId, String id, String name) {
        if (activityTypeDAO.isDuplicateApiName(tenantId, id, name)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE__API_NAME_DUPLICATE_ERROR));
        }
    }

    @Override
    public void publishSyncActivityTypeFieldTask(String tenantId) {
        log.info("publishSyncActivityTypeFieldTask:{}", tenantId);
        String traceId = TraceContext.get().getTraceId();
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            TraceContext.get().setTraceId(traceId);
            Map<String, String> activityTypeMap = activityTypeDAO.all(tenantId, true)
                    .stream()
                    .collect(Collectors.toMap(k -> k.getId().toString(), k -> k.isDeleted() ? String.format("%s-%s" + ACTIVITY_TYPE_DELETED_IDENTIFY, k.getName(), k.getApiName()) : k.getName()));

            if (MapUtils.isEmpty(activityTypeMap)) {
                log.info("activityTypeMap is Empty:{}", tenantId);
                return;
            }
            for (String apiName : SYNC_ACTIVITY_TYPE_OBJECT_API_NAMES) {
                try {
                    IObjectDescribe activityDescribe = describeService.findObject(tenantId, apiName);
                    ObjectDescribeExt activityDescribeExt = ObjectDescribeExt.of(activityDescribe);
                    updateSelectOneFieldDescribe(activityTypeMap, activityDescribe, activityDescribeExt);
                } catch (Exception e) {
                    log.error("同步活动类型字段信息失败", e);
                }
            }
        })).run();
    }

    @SneakyThrows
    private void updateSelectOneFieldDescribe(Map<String, String> activityTypeMap,
                                              IObjectDescribe activityDescribe,
                                              ObjectDescribeExt activityDescribeExt) {
        LanguageReplaceWrapper.doInChinese(() -> {
            for (SelectOne selectOneField : activityDescribeExt.getSelectOneFields()) {
                if (selectOneField.getApiName().equals("activity_type")) {
                    List<ISelectOption> options = selectOneField.getSelectOptions();
                    Map<String, ISelectOption> oldOptionMap = options.stream().collect(Collectors.toMap(ISelectOption::getValue, Function.identity()));

                    String optionsIdentity = toSelectOptionsIdentity(options);
                    String recordTypesIdentity = toActivityTypesIdentity(activityTypeMap);

                    if (!optionsIdentity.equals(recordTypesIdentity)) {
                        options.clear();
                        for (Map.Entry<String, String> entry : activityTypeMap.entrySet()) {
                            ISelectOption newOption = new SelectOption();
                            newOption.setLabel(entry.getValue());
                            newOption.setValue(entry.getKey());
                            if (entry.getValue().contains(ACTIVITY_TYPE_DELETED_IDENTIFY)) {
                                newOption.setNotUsable(true);
                            }
                            if (oldOptionMap.containsKey(entry.getKey())) {
                                newOption.setChildOptions(oldOptionMap.get(entry.getKey()).getChildOptions());
                                Object fontColor = oldOptionMap.get(entry.getKey()).get("font_color");
                                if (Objects.nonNull(fontColor)) {
                                    newOption.set("font_color", fontColor);
                                }
                            }
                            options.add(newOption);
                        }
                        selectOneField.setSelectOptions(options);

                        IObjectDescribe updateResult = null;
                        try {
                            updateResult = objectDescribeService.updateFieldDescribe(activityDescribe, Lists.newArrayList(selectOneField), this.getActionContext());
                        } catch (MetadataServiceException e) {
                            log.info("update err,", e);
                            throw new RuntimeException(e);
                        }

                        log.info("update field : {}, update result : {}", selectOneField.toJsonString(), updateResult.toJsonString());
                    }
                }
            }
        });

    }

    @Override
    public void fillObjectDisplayNameAndRecordTypeDisplayName(String tenantId, ActivityTypeVO activityType) {
        Set<String> apiNames = new HashSet<>();
        for (ActivityNodeVO node : activityType.getActivityNodeList()) {
            apiNames.add(node.getObjectApiName());
        }
        Map<String, IObjectDescribe> describeMap = describeService.findObjects(tenantId, apiNames);
        Map<String, String> describeNameMap = describeMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().getDisplayName()));
        Map<String, Map<String, String>> recordTypeNameMap = describeMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, v -> {
            ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(v.getValue());
            Map<String, String> recordTypeNameMapValue = Maps.newHashMap();
            objectDescribeExt.getRecordTypeField().ifPresent(field -> field.getRecordTypeOptions().forEach(option -> recordTypeNameMapValue.put(option.getApiName(), option.getLabel())));
            return recordTypeNameMapValue;
        }));
        for (ActivityNodeVO node : activityType.getActivityNodeList()) {
            node.setObjectDisplayName(describeNameMap.getOrDefault(node.getObjectApiName(), "--"));
            node.setObjectRecordTypeName(recordTypeNameMap.getOrDefault(node.getObjectApiName(), Maps.newHashMap()).getOrDefault(node.getObjectRecordType(), "--"));
        }
    }

    @Override
    public void fillNodeTemplateDescription(String tenantId, ActivityTypeVO activityType) {
        Set<String> templateIds = new HashSet<>();
        for (ActivityNodeVO node : activityType.getActivityNodeList()) {
            templateIds.add(node.getTemplateId());
        }
        List<ActivityNodeTemplatePO> templates = activityNodeTemplateDAO.list(tenantId, templateIds);
        Map<String, String> descriptionMap = templates.stream().collect(Collectors.toMap(k -> k.getId().toString(), ActivityNodeTemplatePO::getDescription));
        for (ActivityNodeVO node : activityType.getActivityNodeList()) {
            node.setDescription(descriptionMap.getOrDefault(node.getTemplateId(), "--"));
        }
    }

    @Override
    public void fillActivityReportData(String tenantId, String activityId, ActivityTypeVO vo) {
        for (ActivityNodeVO node : vo.getActivityNodeList()) {
            if (NodeType.CUSTOM.value().equals(node.getType())) {
                node.setActivityPlanReportData(ActivityReportLoaderFactory.resolve(node.getType()).loadActivityPlanReport(tenantId, activityId, node.getObjectApiName(), node.getReferenceActivityFieldApiName(), node.getObjectRecordType()));
            } else {
                node.setActivityPlanReportData(ActivityReportLoaderFactory.resolve(node.getType()).loadActivityPlanReport(tenantId, activityId));
            }
        }
    }

    @Override
    public Map<String, IObjectDescribe> loadDescribeMap(String tenantId, String activityTypeId) {
        ActivityTypePO activityType = activityTypeDAO.get(tenantId, activityTypeId);
        return loadDescribeMap(tenantId, activityType);
    }

    @Override
    public Map<String, IObjectDescribe> loadDescribeMap(String tenantId, ActivityTypePO activityType) {
        Set<String> apiNames = new HashSet<>();
        for (ActivityNodeEntity activityNode : activityType.getActivityNodes()) {
            apiNames.add(activityNode.getObjectApiName());
        }
        return describeService.findObjects(tenantId, apiNames);
    }

    @Override
    public List<IObjectData> findActivityByActivityTypeId(String tenantId, String activityTypeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityTypeFilter.setFieldValues(Lists.newArrayList(activityTypeId));
        activityTypeFilter.setOperator(Operator.EQ);

        query.setFilters(Lists.newArrayList(activityTypeFilter));
        query.setOrders(Lists.newArrayList());

        List<IObjectData> activityObjs = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, query).getData();
        if (activityObjs == null) {
            return Lists.newArrayList();
        }
        return activityObjs;
    }

    public List<IObjectData> findActivityUnifiedCaseByActivityTypeId(String tenantId, String activityTypeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMActivityUnifiedCaseFields.ACTIVITY_TYPE);
        activityTypeFilter.setFieldValues(Lists.newArrayList(activityTypeId));
        activityTypeFilter.setOperator(Operator.EQ);

        query.setFilters(Lists.newArrayList(activityTypeFilter));
        query.setOrders(Lists.newArrayList());

        List<IObjectData> activityObjs = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, query).getData();
        if (activityObjs == null) {
            return Lists.newArrayList();
        }
        return activityObjs;
    }

    @Override
    public List<IObjectData> findUnCloseActivityByActivityTypeIds(String tenantId, String activityTypeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityTypeFilter.setFieldValues(Lists.newArrayList(activityTypeId));
        activityTypeFilter.setOperator(Operator.EQ);

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSED_STATUS);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));
        closeStatusFilter.setOperator(Operator.EQ);

        query.setFilters(Lists.newArrayList(activityTypeFilter, closeStatusFilter));
        query.setOrders(Lists.newArrayList());

        List<IObjectData> activityObjs = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, query).getData();
        if (activityObjs == null) {
            return Lists.newArrayList();
        }
        return activityObjs;
    }

    @Override
    public IObjectData findFundAccountObjById(String tenantId, String fundAccountId) {
        IObjectData findObjectData = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(tenantId), fundAccountId, ApiNames.FUND_ACCOUNT_OBJ);
        if (Objects.isNull(findObjectData)) {
            return null;
        }
        return findObjectData;
    }

    @Override
    public String checkValidation(String tenantId, ActivityTypePO activityType) {
        // 检验活动类型
        String name = activityType.getName();

        List<String> nodeApiNames = activityType.getActivityNodes().stream().map(ActivityNodeEntity::getObjectApiName).collect(Collectors.toList());

        Map<String, IObjectDescribe> describeMap = describeService.findObjects(tenantId, nodeApiNames);
        String prefix = String.format("[%s]活动类型中的", name);
        String suffix = "导致该节点暂时无法使用。会影响活动类型的正常使用，请及时处理。";
        StringBuilder errorMessage = new StringBuilder();

        for (int i = 1; i < activityType.getActivityNodes().size(); i++) {

            ActivityNodeEntity preNode = activityType.getActivityNodes().get(i - 1);
            ActivityNodeEntity node = activityType.getActivityNodes().get(i);
            if (node.getObjectApiName().equals(ApiNames.TPM_ACTIVITY_OBJ)) {
                continue;
            }

            IObjectDescribe describe = describeMap.get(node.getObjectApiName());
            validationObjOrFile(activityType, node, errorMessage, describe, null, 1, false, false, "");
            if (describe == null) {
                continue;
            }

            List<IFieldDescribe> referenceFields = describe.getFieldDescribes().stream().filter(o -> o.getType().equals(IFieldType.OBJECT_REFERENCE)).collect(Collectors.toList());

            //关联前一个对象子段
            boolean storeNodeStatus = checkStoreNodeStatus(node.getType(), preNode.getObjectApiName());
            if (!node.getType().equals(NodeType.WRITE_OFF.value())
                    && !preNode.getObjectApiName().equals(ApiNames.TPM_ACTIVITY_OBJ) && !storeNodeStatus) {
                IFieldDescribe referencePreObj = referenceFields.stream().filter(o -> o.get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class).equals(preNode.getObjectApiName())).findAny().orElse(null);
                validationObjOrFile(activityType, node, errorMessage, describe, referencePreObj, 2, false, true, "");
            }

            //关联活动方案对象子段
            IFieldDescribe activityObj = referenceFields.stream().filter(o -> o.get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class).equals(ApiNames.TPM_ACTIVITY_OBJ)).findAny().orElse(null);
            validationObjOrFile(activityType, node, errorMessage, describe, activityObj, 2, true, false, "");

            if (node.getType().equals(NodeType.AUDIT.value())) {

                if (Strings.isNullOrEmpty(node.getActivityProofAuditConfig().getAuditSourceConfig().getTemplateId())) {
                    judgeAuditNodeOfSourceNode(activityType, errorMessage, node);
                    continue;
                }

                String referenceAuditSourceFieldApiName = node.getActivityProofAuditConfig().getAuditSourceConfig().getReferenceAuditSourceFieldApiName();
                IFieldDescribe referenceAuditSourceField = describe.getFieldDescribe(referenceAuditSourceFieldApiName);
                validationObjOrFile(activityType, node, errorMessage, describe, referenceAuditSourceField, 2, false, false, referenceAuditSourceFieldApiName);

                String masterApiName = node.getActivityProofAuditConfig().getAuditSourceConfig().getMasterApiName();
                if (!Strings.isNullOrEmpty(masterApiName)) {
                    IObjectDescribe masterDescribe = serviceFacade.findObject(tenantId, masterApiName);
                    String accountFieldApiName = node.getActivityProofAuditConfig().getAuditSourceConfig().getAccountFieldApiName();
                    IFieldDescribe accountField = masterDescribe.getFieldDescribe(accountFieldApiName);
                    validationObjOrFile(activityType, node, errorMessage, masterDescribe, accountField, 2, false, false, accountFieldApiName);
                }

            }

            if (node.getType().equals(NodeType.WRITE_OFF.value())) {
                String sourceApiName = node.getActivityWriteOffConfig().getWriteOffSourceConfig().getApiName();
                if (!Strings.isNullOrEmpty(sourceApiName)) {
                    IObjectDescribe sourceDescribe = serviceFacade.findObject(tenantId, sourceApiName);
                    String referenceWriteOffFieldApiName = node.getActivityWriteOffConfig().getWriteOffSourceConfig().getReferenceWriteOffFieldApiName();
                    IFieldDescribe fieldDescribe = sourceDescribe.getFieldDescribe(referenceWriteOffFieldApiName);
                    validationObjOrFile(activityType, node, errorMessage, sourceDescribe, fieldDescribe, 2, false, false, referenceWriteOffFieldApiName);

                    //被核销节点非活动检核节点，客户必填
                    if (!sourceApiName.equals(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ)) {
                        String accountFieldApiName = node.getActivityWriteOffConfig().getWriteOffSourceConfig().getAccountFieldApiName();
                        IFieldDescribe accountField = sourceDescribe.getFieldDescribe(accountFieldApiName);
                        validationObjOrFile(activityType, node, errorMessage, sourceDescribe, accountField, 2, false, false, accountFieldApiName);
                    }
                }
            }

            if (node.getType().equals(NodeType.STORE_WRITE_OFF.value())) {
                if (!preNode.getType().equals(NodeType.PROOF.value()) && !preNode.getType().equals(NodeType.AUDIT.value())) {
                    judgeStoreWriteNodeOfPreNode(activityType, errorMessage, node);
                }
            }
        }

        if (errorMessage.length() > 0) {
            return prefix + errorMessage.toString() + suffix;
        } else {
            if (activityType.getExceptionStatus().equals(ExceptionStatusType.ERROR.value())) {
                activityType.setExceptionStatus(ExceptionStatusType.NORMAL.value());
                for (ActivityNodeEntity node : activityType.getActivityNodes()) {
                    node.setExceptionStatus(ExceptionStatusType.NORMAL.value());
                }
            }
        }
        return "";
    }

    private boolean checkStoreNodeStatus(String type, String objectApiName) {
        log.info("node type = {}, objectApiName is {}", type, objectApiName);
        return type.equals(NodeType.STORE_WRITE_OFF.value());
    }

    private void judgeAuditNodeOfSourceNode(ActivityTypePO activityType, StringBuilder errorMessage, ActivityNodeEntity node) {
        activityType.setExceptionStatus(ExceptionStatusType.ERROR.value());
        node.setExceptionStatus(ExceptionStatusType.ERROR.value());
        node.setNodeExceptionInfo(new NodeExceptionInfoEntity(ExceptionStatusType.NORMAL.value(), ExceptionStatusType.ERROR.value()));
        errorMessage.append("[活动检核]节点关联的被检核节点不存在，");
    }

    private void judgeStoreWriteNodeOfPreNode(ActivityTypePO activityType, StringBuilder errorMessage, ActivityNodeEntity node) {
        activityType.setExceptionStatus(ExceptionStatusType.ERROR.value());
        node.setExceptionStatus(ExceptionStatusType.ERROR.value());
        node.setNodeExceptionInfo(new NodeExceptionInfoEntity(ExceptionStatusType.NORMAL.value(), ExceptionStatusType.ERROR.value()));
        errorMessage.append("[门店费用核销]节点关联的前一个节点必须是[活动检核]或[活动举证]");
    }

    public void validationObjOrFile(ActivityTypePO activityType,
                                    ActivityNodeEntity node,
                                    StringBuilder errorMessage,
                                    IObjectDescribe nodeDescribe,
                                    IFieldDescribe fieldDescribe,
                                    int type,
                                    boolean isActivity,
                                    boolean isPreNode,
                                    String fileApiName
    ) {


        if (type == 1) {
            //对象描述
            if (Objects.isNull(nodeDescribe)) {
                activityType.setExceptionStatus(ExceptionStatusType.ERROR.value());
                node.setExceptionStatus(ExceptionStatusType.ERROR.value());
                errorMessage.append(String.format("[%s]活动节点关联的【%s】对象不存在，", node.getName(), node.getObjectApiName()));
            } else {
                if (Boolean.FALSE.equals(nodeDescribe.isActive())) {
                    activityType.setExceptionStatus(ExceptionStatusType.ERROR.value());
                    node.setExceptionStatus(ExceptionStatusType.ERROR.value());
                    errorMessage.append(String.format("[%s]活动节点关联的【%s】对象被禁用，", node.getName(), nodeDescribe.getDisplayName()));
                }
            }
        } else if (type == 2) {
            //字段描述校验
            if (Objects.isNull(fieldDescribe)) {
                activityType.setExceptionStatus(ExceptionStatusType.ERROR.value());
                node.setExceptionStatus(ExceptionStatusType.ERROR.value());
                if (isPreNode) {
                    errorMessage.append(String.format("[%s]活动节点关联的【%s】对象关联前一个节点对象字段被删除，", node.getName(), nodeDescribe.getDisplayName()));
                } else {
                    errorMessage.append(String.format("[%s]活动节点关联的【%s】对象关联字段【%s】被删除，", node.getName(), nodeDescribe.getDisplayName(), fileApiName));
                }
            } else if (Boolean.FALSE.equals(fieldDescribe.isActive())) {
                activityType.setExceptionStatus(ExceptionStatusType.ERROR.value());
                node.setExceptionStatus(ExceptionStatusType.ERROR.value());
                errorMessage.append(String.format("[%s]活动节点【%s】对象关联字段【%s】被禁用，", node.getName(), nodeDescribe.getDisplayName(), fieldDescribe.getLabel()));
            }

            if (!Strings.isNullOrEmpty(node.getExceptionStatus()) && node.getExceptionStatus().equals(ExceptionStatusType.ERROR.value()) && (isActivity || isPreNode)) {
                NodeExceptionInfoEntity nodeExceptionInfo = node.getNodeExceptionInfo();
                if (isActivity) {
                    nodeExceptionInfo.setObjectReferenceActivity(ExceptionStatusType.ERROR.value());
                } else {
                    nodeExceptionInfo.setObjectPreNode(ExceptionStatusType.ERROR.value());
                }
                node.setNodeExceptionInfo(nodeExceptionInfo);
            }
        }
    }


    @Override
    public boolean editAble(String tenantId, String id) {
        List<IObjectData> activityByActivityTypeIds = findActivityByActivityTypeId(tenantId, id);
        if (CollectionUtils.notEmpty(activityByActivityTypeIds)) {
            log.info("该活动类型下已有活动申请，不可编辑 tenantId={},id={}", tenantId, id);
            return false;
        }
        List<IObjectData> unifiedCaseByActivityTypeId = findActivityUnifiedCaseByActivityTypeId(tenantId, id);
        if (CollectionUtils.notEmpty(unifiedCaseByActivityTypeId)) {
            log.info("该活动类型下已有活动方案，不可编辑 tenantId={},id={}", tenantId, id);
            return false;
        }
        return true;
    }

    @Override
    public void fieldEditAbleValidation(String tenantId, String id, ActivityTypePO old, ActivityTypeVO activityType) {
        List<IObjectData> activityByActivityTypeIds = findActivityByActivityTypeId(tenantId, id);
        if (CollectionUtils.notEmpty(activityByActivityTypeIds)) {
            log.info("该活动类型下已有活动申请 tenantId={},id={}", tenantId, id);

            boolean isCloseStatus = false;
            for (IObjectData objectData : activityByActivityTypeIds) {
                if (TPMActivityFields.CLOSE_STATUS__UNCLOSED.equals(objectData.get(TPMActivityFields.CLOSE_STATUS, String.class))) {
                    isCloseStatus = true;
                    break;
                }
            }

            ActivityTypePO currentPo = ActivityTypePO.fromVO(activityType);

            List<ActivityNodeEntity> oleNodes = old.getActivityNodes();
            List<ActivityNodeEntity> currentNodes = currentPo.getActivityNodes();

            if (CollectionUtils.empty(oleNodes) || CollectionUtils.empty(currentNodes) || oleNodes.size() != currentNodes.size()) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_MANAGER_4));
            }
            for (int i = 0; i < oleNodes.size(); i++) {
                ActivityNodeEntity oleEntity = oleNodes.get(i);
                ActivityNodeEntity currentEntity = currentNodes.get(i);
                if (!oleEntity.getTemplateId().equals(currentEntity.getTemplateId())) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_MANAGER_5));
                }
                if (NodeType.AUDIT.value().equals(oleEntity.getType())) {
                    if (!oleEntity.getActivityProofAuditConfig().getAuditModeConfig().getAuditMode().equals(
                            currentEntity.getActivityProofAuditConfig().getAuditModeConfig().getAuditMode())
                            && isCloseStatus) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_MANAGER_6));
                    }
                }
                //校验 门店费用核销 依据对象是否已有数据产生，有数据产生则不可在编辑
                if (NodeType.STORE_WRITE_OFF.value().equals(oleEntity.getType())) {
                    if (verifyStoreRefApiName(oleEntity, currentEntity) &&
                            verifyExistsStoreWriteOff(id, tenantId)) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_MANAGER_7));
                    }
                }
            }
            Map<String, String> oldMap = getFileMap(old);
            Map<String, String> newMap = getFileMap(currentPo);

            if (!oldMap.equals(newMap)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_MANAGER_8));
            }
        }
    }

    private boolean verifyStoreRefApiName(ActivityNodeEntity old, ActivityNodeEntity currentPo) {

        String apiName = old.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().getApiName();
        String currentApiName = currentPo.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().getApiName();
        log.info("apiName is {}, currentApiName is {}", apiName, currentApiName);
        return !apiName.equals(currentApiName);
    }

    @Override
    public boolean verifyExistsStoreWriteOff(String id, String tenantId) {

        if (Strings.isNullOrEmpty(id)) {
            return false;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMStoreWriteOffFields.ACTIVITY_TYPE);
        activityTypeFilter.setOperator(Operator.EQ);
        activityTypeFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(activityTypeFilter));
        IActionContext actionContext = ActionContextUtil.getNewContext(tenantId);
        actionContext.setUserId(User.systemUser(tenantId).getUserId());
        actionContext.setPrivilegeCheck(false);

        try {
            return !serviceFacade.findBySearchTemplateQueryWithFields(
                    actionContext,
                    ApiNames.TPM_STORE_WRITE_OFF_OBJ,
                    query,
                    Lists.newArrayList(CommonFields.ID)
            ).getData().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public void deleteReference(String tenantId, ActivityTypePO po) {
        List<ActivityNodeEntity> nodes = po.getActivityNodes();
        for (int index = 0; index < nodes.size(); index++) {

            ActivityNodeEntity node = nodes.get(index);
            NodeType nodeType = NodeType.of(node.getType());
            if (nodeType.equals(NodeType.PLAN) || nodeType.equals(NodeType.PLAN_TEMPLATE)) {
                continue;
            }

            ActivityNodeEntity preNode = nodes.get(index - 1);
            String preNodeApiName = preNode.getObjectApiName();

            if (node.getObjectApiName().equals(ApiNames.TPM_DEALER_ACTIVITY_COST) || node.getObjectApiName().equals(ApiNames.TPM_STORE_WRITE_OFF_OBJ)) {
                continue;
            }

            List<PaasReferenceBatchDelete.BatchDeleteArg> reference = Lists.newArrayList();
            IObjectDescribe describe = describeLogicService.findObject(tenantId, node.getObjectApiName());
            for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
                if (fieldDescribe.getType().equals(IFieldType.OBJECT_REFERENCE)) {
                    String targetApiName = fieldDescribe.get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class);
                    if (preNodeApiName.equals(targetApiName) || targetApiName.equals(ApiNames.TPM_ACTIVITY_OBJ)) {
                        String fieldApiName = fieldDescribe.getApiName();
                        if (fieldApiName.endsWith("__c")) {
                            PaasReferenceBatchDelete.BatchDeleteArg referenceCommonArg = new PaasReferenceBatchDelete.BatchDeleteArg();
                            referenceCommonArg.setSourceType("TPMDesigner");
                            referenceCommonArg.setSourceValue(String.format("%s--%s--%s--%s", po.getApiName(), node.getType(), preNode.getType(), fieldApiName));
                            referenceCommonArg.setTargetType("Describe.Field");
                            referenceCommonArg.setTargetValue(String.format("%s.%s", node.getObjectApiName(), fieldApiName));
                            reference.add(referenceCommonArg);
                        }
                    }
                }
            }

            if (CollectionUtils.notEmpty(reference)) {
                paasReferenceService.deleteReference(tenantId, reference);
            }
        }
    }

    @Override
    public void createReference(String tenantId, ActivityTypeVO vo) {
        List<ActivityNodeVO> nodes = vo.getActivityNodeList();
        List<PaasReferenceCommonArg> reference = Lists.newArrayList();
        for (int index = 0; index < nodes.size(); index++) {

            ActivityNodeVO node = nodes.get(index);
            NodeType nodeType = NodeType.of(node.getType());

            if (nodeType.equals(NodeType.PLAN_TEMPLATE) ||
                    nodeType.equals(NodeType.PLAN) ||
                    node.getObjectApiName().equals(ApiNames.TPM_DEALER_ACTIVITY_COST) ||
                    node.getObjectApiName().equals(ApiNames.TPM_STORE_WRITE_OFF_OBJ)) {
                continue;
            }

            ActivityNodeVO preNode = nodes.get(index - 1);
            String preNodeApiName = preNode.getObjectApiName();

            IObjectDescribe describe = describeLogicService.findObject(tenantId, node.getObjectApiName());
            for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
                if (fieldDescribe.getType().equals(IFieldType.OBJECT_REFERENCE)) {
                    String targetApiName = fieldDescribe.get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class);
                    if (preNodeApiName.equals(targetApiName) || targetApiName.equals(ApiNames.TPM_ACTIVITY_OBJ)) {
                        String fieldApiName = fieldDescribe.getApiName();
                        if (fieldApiName.endsWith("__c")) {

                            PaasReferenceCommonArg referenceCommonArg = new PaasReferenceCommonArg();
                            referenceCommonArg.setSourceType("TPMDesigner");
                            referenceCommonArg.setSourceLabel(String.format("营销活动.活动类型[%s]", vo.getName()));
                            referenceCommonArg.setSourceValue(String.format("%s--%s--%s--%s", vo.getApiName(), node.getType(), preNode.getType(), fieldApiName));
                            referenceCommonArg.setTargetType("Describe.Field");
                            referenceCommonArg.setTargetValue(String.format("%s.%s", node.getObjectApiName(), fieldApiName));
                            reference.add(referenceCommonArg);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.notEmpty(reference)) {
            PaasReferenceCreate.Arg referenceCreate = new PaasReferenceCreate.Arg();
            referenceCreate.setItems(reference);
            paasReferenceService.createReference(tenantId, referenceCreate);
        }
    }

    @Override
    public boolean getEnableChargeUp(String tenantId) {
        GetConfigValueByKey.Arg arg = new GetConfigValueByKey.Arg();
        arg.setKey("is_customer_account_enable");
        GetConfigValueByKey.Result result = paasDataProxy.getConfigValueByKey(Integer.parseInt(tenantId), -10000, arg);
        if (result.getCode() != 0) {
            return false;
        }
        String value = result.getData().getValue();

        return "2".equals(value);
    }

    @Override
    public boolean getEnableAi(String tenantId) {
        return tpm2Service.existFmcgAiProductRecognitionAppLicenseCode(Integer.parseInt(tenantId));
    }

    @Override
    public List<ActivityTypeExt> queryActivityTypesByModelIdOrRuleId(String tenantId, String modelId, String ruleId) {
        List<ActivityTypePO> activityTypePOS = activityTypeDAO.queryActivityTypesByModelIdOrRuleId(tenantId, modelId, ruleId);
        return activityTypePOS.stream().map(ActivityTypeExt::of).collect(Collectors.toList());
    }

    private Map<String, String> getFileMap(ActivityTypePO activityType) {
        Map<String, String> map = Maps.newHashMap();
        for (ActivityNodeEntity activityNodeEntity : activityType.getActivityNodes()) {
            NodeType nodeType = NodeType.of(activityNodeEntity.getType());
            switch (nodeType) {
                case PROOF:
                    ActivityProofConfigEntity activityProofConfig = activityNodeEntity.getActivityProofConfig();
                    map.put("calculate_type", activityProofConfig.getCostCalculateConfig().getCalculateType());
                    break;
                case AUDIT:
                    ActivityProofAuditConfigEntity activityProofAuditConfig = activityNodeEntity.getActivityProofAuditConfig();
                    map.put("audit_template_id", activityProofAuditConfig.getAuditSourceConfig().getTemplateId());
                    map.put("master_api_name", activityProofAuditConfig.getAuditSourceConfig().getMasterApiName());
                    map.put("reference_audit_source_field_api_name", activityProofAuditConfig.getAuditSourceConfig().getReferenceAuditSourceFieldApiName());
                    map.put("reference_audit_source_detail_field_api_name", activityProofAuditConfig.getAuditSourceConfig().getReferenceAuditSourceDetailFieldApiName());
                    map.put("account_field_api_name", activityProofAuditConfig.getAuditSourceConfig().getAccountFieldApiName());
                    break;
                case WRITE_OFF:
                    ActivityWriteOffConfigEntity activityWriteOffConfig = activityNodeEntity.getActivityWriteOffConfig();
                    map.put("api_name", activityWriteOffConfig.getWriteOffSourceConfig().getApiName());
                    map.put("reference_write_off_field_api_name", activityWriteOffConfig.getWriteOffSourceConfig().getReferenceWriteOffFieldApiName());
                    map.put("calculateType", activityWriteOffConfig.getWriteOffSourceConfig().getCalculateType());
                    break;
                case COST_ASSIGN:
                    ActivityCostAssignConfigEntity activityCostAssignConfigEntity = activityNodeEntity.getActivityCostAssignConfigEntity();
                    map.put("accept_status", activityCostAssignConfigEntity.getActivityCostAssignAcceptConfig().getAcceptStatus().toString());
                    break;
                case STORE_WRITE_OFF:
                    ActivityStoreWriteOffConfigEntity activityStoreWriteOffConfig = activityNodeEntity.getActivityStoreWriteOffConfig();
                    map.put("api_name", activityStoreWriteOffConfig.getStoreWriteOffSourceConfig().getApiName());
                    break;
                default:
                    break;
            }
        }
        return map;
    }

    @Override
    public List<String> queryProofActivityTypeIds(String tenantId) {
        return activityTypeDAO.queryProofActivityTypeIds(tenantId);
    }

    @Override
    public List<ActivityTypeExt> queryActivityTypeContainsAudit(String tenantId) {
        List<ActivityTypePO> activityTypePOS = activityTypeDAO.queryActivityTypesContainsAudit(tenantId);
        return activityTypePOS.stream().map(ActivityTypeExt::of).collect(Collectors.toList());
    }

    @Override
    public List<ActivityTypeExt> queryActivityTypeContainsAgreement(String tenantId) {
        List<ActivityTypePO> activityTypePOS = activityTypeDAO.queryActivityTypesContainsXXNodeType(tenantId, NodeType.AGREEMENT.value());
        return activityTypePOS.stream().map(ActivityTypeExt::of).collect(Collectors.toList());
    }

    @Override
    public List<String> tryInitSystemTemplate(String tenantId, Integer employeeId) {
        List<String> systemActivityTypes = activityTypeDAO.querySystemActivityType(tenantId);
        List<String> activityTypeIds = Lists.newArrayList();

        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        if (ea.endsWith("sandbox")) {
            return activityTypeIds;
        }

        if (tpm2Service.existTPMLicenseTenant(Integer.valueOf(tenantId))) {
            boolean storeWriteOff = isStoreWriteOff(tenantId);
            // 初始化活动节点
            activityNodeTemplateDAO.initSystemTemplate(tenantId, employeeId, storeWriteOff);

            if (!systemActivityTypes.contains(PreActivityType.TYPE_ACTIVITY.apiName())) {
                //陈列类活动
                ActivityTypePO displayPO = getSystemActivityTypePO(tenantId, employeeId, PreActivityType.TYPE_ACTIVITY.order());
                String displayId = activityTypeDAO.add(tenantId, employeeId, displayPO);
                activityTypeIds.add(displayId);
            }

            if (!systemActivityTypes.contains(PreActivityType.TYPE_ACTIVITY_LIVE.apiName())) {
                //直播类活动
                ActivityTypePO liveTelecastPO = getSystemActivityTypePO(tenantId, employeeId, PreActivityType.TYPE_ACTIVITY_LIVE.order());
                String liveTelecastId = activityTypeDAO.add(tenantId, employeeId, liveTelecastPO);
                activityTypeIds.add(liveTelecastId);
            }
        }

        if (tpm2Service.existTPMCodeLicenseTenant(Integer.valueOf(tenantId))) {
            activityNodeTemplateDAO.initSystemTPModeTemplate(tenantId, employeeId);

            List<PreActivityType> preActivityTypes = Lists.newArrayList(PreActivityType.TYPE_ACTIVITY_SCAN_CODE_GET_REWARD,
                    PreActivityType.TYPE_ACTIVITY_STOCK_UP_REWARD,
                    PreActivityType.TYPE_ACTIVITY_BIG_DATE);
            for (PreActivityType preActivityType : preActivityTypes) {
                if (!systemActivityTypes.contains(preActivityType.apiName())) {
                    ActivityTypePO codeTypePO = getSystemActivityTypePO(tenantId, employeeId, preActivityType.order());
                    String codeTypeId = activityTypeDAO.add(tenantId, employeeId, codeTypePO);
                    activityTypeIds.add(codeTypeId);
                }
            }
        }


        return activityTypeIds;
    }

    private ActivityTypePO getSystemActivityTypePO(String tenantId, Integer employeeId, int activityType) {
        ActivityTypePO po = new ActivityTypePO();

        PreActivityType preActivityType = PreActivityType.of(activityType);
        switch (preActivityType) {
            case TYPE_ACTIVITY:
                po.setName("陈列类活动");
                po.setApiName(PreActivityType.TYPE_ACTIVITY.apiName());
                po.setUniqueId(DISPLAY_ACTIVITY_TYPE_UNIQUE_ID);
                po.setTemplateId("display.complex");
                break;
            case TYPE_ACTIVITY_LIVE:
                po.setName("直播类活动");
                po.setApiName(PreActivityType.TYPE_ACTIVITY_LIVE.apiName());
                po.setUniqueId(LIVE_ACTIVITY_TYPE_UNIQUE_ID);
                po.setTemplateId("online.simple");
                break;
            case TYPE_ACTIVITY_SCAN_CODE_GET_REWARD:
                po.setName("码上有礼");
                po.setApiName(PreActivityType.TYPE_ACTIVITY_SCAN_CODE_GET_REWARD.apiName());
                //po.setUniqueId(LIVE_ACTIVITY_TYPE_UNIQUE_ID);
                po.setTemplateId("reward.scan_code_get_reward");
                break;
            case TYPE_ACTIVITY_STOCK_UP_REWARD:
                po.setName("囤货激励");
                po.setApiName(PreActivityType.TYPE_ACTIVITY_STOCK_UP_REWARD.apiName());
                //po.setUniqueId("6498fc7249a39d0001c748e0");
                po.setTemplateId("reward.stock_up_reward");
                break;
            case TYPE_ACTIVITY_BIG_DATE:
                po.setName("临期减价处理");
                po.setApiName(PreActivityType.TYPE_ACTIVITY_BIG_DATE.apiName());
                //po.setUniqueId();
                po.setTemplateId("reward.big_date");
                break;
            default:
                break;
        }

        po.setDescription("系统预置");
        po.setVersion(0);
        po.setPackageType(PackageType.SYSTEM.value());
        po.setStatus(StatusType.NORMAL.value());
        po.setExceptionStatus(ExceptionStatusType.NORMAL.value());
        po.setDepartmentIds(Lists.newArrayList(999999));
        po.setRoleIds(Lists.newArrayList("personnelrole"));
        po.setScopeDescription("部门：全公司;");

        List<ActivityNodeEntity> activityNodes = getSysTemActivityNodes(tenantId, activityType);
        po.setActivityNodes(activityNodes);

        return po;
    }

    private boolean isStoreWriteOff(String tenantId) {
        boolean storeWriteOff = false;
        try {
            IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, ApiNames.TPM_STORE_WRITE_OFF_OBJ);
            if (objectDescribe != null) {
                storeWriteOff = true;
            }
        } catch (Exception e) {
            log.error("findObject tenantId is {} find error.", tenantId);
        }
        return storeWriteOff;
    }

    @Override
    public boolean getEnableAuditMode(String tenantId, String id) {
        List<IObjectData> unCloseActivityByActivityTypeIds = findUnCloseActivityByActivityTypeIds(tenantId, id);
        if (CollectionUtils.notEmpty(unCloseActivityByActivityTypeIds)) {
            log.info("该活动类型下有未结案的活动申请，不可编辑活动检核方式 tenantId={},id={}", tenantId, id);
            return false;
        }
        return true;
    }

    @Override
    public void publishSyncActivityTypeExceptionTask(String tenantId, String errorMessage, ActivityTypePO po) {
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {

            Integer exceptionCount = po.getExceptionCount();
            po.setExceptionCount(exceptionCount == null ? 1 : exceptionCount + 1);

            activityTypeDAO.editExceptionStatus(
                    tenantId,
                    po.getId().toHexString(),
                    po.getExceptionStatus(),
                    po.getExceptionCount(),
                    po.getActivityNodes()
            );

            if (!Strings.isNullOrEmpty(errorMessage) && po.getExceptionStatus().equals(ExceptionStatusType.ERROR.value())) {
                String nodeName = po.getName();
                String objectDisplayName = po.getDescription();
                String referenceFieldApiName = po.getScopeDescription();
                SessionContentSyncErrorActivity sessionContentSyncErrorActivity = new SessionContentSyncErrorActivity(
                        nodeName, objectDisplayName, referenceFieldApiName, errorMessage, true, null);

                sessionSendService.doSendSessionToSystemAdmins(tenantId, sessionContentSyncErrorActivity, 3);
            }
        })).run();
    }

    @Override
    public boolean judgeOpenCostAssignConfig(String tenantId) {
        GetTPMConfig.Args args = new GetTPMConfig.Args();
        args.setTenantId(tenantId);
        try {
            GetTPMConfig.Result tpmConfig = shopMMService.getTPMConfig(args);
            if (tpmConfig == null) {
                log.info("获取费用签收确认状态失败： result=null");
                return false;
            }
            if (!ObjectUtils.isEmpty(tpmConfig.isFeeReceiptConfirm())) {
                return tpmConfig.isFeeReceiptConfirm();
            }
        } catch (Exception e) {
            log.error("获取费用签收确认状态异常:tenantId：{}", tenantId, e);
        }
        return false;
    }

    private List<ActivityNodeEntity> getSysTemActivityNodes(String tenantId, int activityType) {
        List<ActivityNodeEntity> activityNodes = Lists.newArrayList();

        List<ActivityNodeTemplateVO> data = activityNodeTemplateDAO.sysTemList(tenantId)
                .stream()
                .sorted(Comparator.comparing(v -> NodeType.getOrder(v.getType())))
                .map(ActivityNodeTemplateVO::fromPO)
                .collect(Collectors.toList());

        activityNodeTemplateManager.fillObjectDisplayName(tenantId, data);
        activityNodeTemplateManager.fillRelatedActivityTypes(tenantId, data);

        String proofNodeTemplateId = "";
        String auditNodeTemplateId = "";
        List<Integer> codeActivityTypes = Lists.newArrayList(3, 4, 5);
        for (ActivityNodeTemplateVO nodeTemplate : data) {
            if (NodeType.COST_ASSIGN.value().equals(nodeTemplate.getType())
                    || NodeType.STORE_WRITE_OFF.value().equals(nodeTemplate.getType())
                    || NodeType.PLAN_TEMPLATE.value().equals(nodeTemplate.getType())) {
                continue;
            }
            NodeType nodeType = NodeType.of(nodeTemplate.getType());
            if (activityType == 2 && !nodeType.value().equals(NodeType.PLAN.value()) && !nodeType.value().equals(NodeType.WRITE_OFF.value())) {
                continue;
            }

            if (codeActivityTypes.contains(activityType) && !nodeType.value().equals(NodeType.PLAN.value())) {
                continue;
            }

            ActivityNodeEntity activityNodeEntity = new ActivityNodeEntity();
            /*basic*/
            activityNodeEntity.setName(nodeTemplate.getName());
            activityNodeEntity.setTemplateId(nodeTemplate.getId());
            activityNodeEntity.setPackageType(nodeTemplate.getPackageType());
            activityNodeEntity.setType(nodeTemplate.getType());
            activityNodeEntity.setExceptionStatus(nodeTemplate.getExceptionStatus());
            activityNodeEntity.setNodeExceptionInfo(new NodeExceptionInfoEntity(ExceptionStatusType.NORMAL.value(), ExceptionStatusType.NORMAL.value()));
            activityNodeEntity.setObjectApiName(nodeTemplate.getObjectApiName());
            activityNodeEntity.setObjectRecordType("default__c");

            if (codeActivityTypes.contains(activityType) && nodeType.value().equals(NodeType.PLAN.value())) {
                switch (activityType) {
                    case 3:
                        activityNodeEntity.setObjectRecordType("scan_code_get_reward__c");
                        break;
                    case 4:
                        activityNodeEntity.setObjectRecordType("stock_up_reward__c");
                        break;
                    case 5:
                        activityNodeEntity.setObjectRecordType("big_date__c");
                        break;
                }
            }

            switch (nodeType) {
                case PLAN:
                    activityNodeEntity.setReferenceActivityFieldApiName("");
                    break;
                case AGREEMENT:
                    activityNodeEntity.setReferenceActivityFieldApiName(TPMActivityAgreementFields.ACTIVITY_ID);
                    break;
                case PROOF:
                    proofNodeTemplateId = nodeTemplate.getId();
                    activityNodeEntity.setReferenceActivityFieldApiName(TPMActivityProofFields.ACTIVITY_ID);
                    initProofNode(activityNodeEntity);
                    break;
                case AUDIT:
                    auditNodeTemplateId = nodeTemplate.getId();
                    activityNodeEntity.setReferenceActivityFieldApiName(TPMActivityProofAuditFields.ACTIVITY_ID);
                    initAuditNode(proofNodeTemplateId, activityNodeEntity);
                    break;
                case WRITE_OFF:
                    activityNodeEntity.setReferenceActivityFieldApiName(TPMDealerActivityCostFields.ACTIVITY_ID);
                    initWriteOff(auditNodeTemplateId, activityNodeEntity, activityType);
                    break;
                default:
            }
            activityNodes.add(activityNodeEntity);
        }
        return activityNodes;
    }

    private void initWriteOff(String auditNodeTemplateId, ActivityNodeEntity activityNodeEntity, int activityType) {
        ActivityWriteOffSourceConfigEntity activityWriteOffSourceConfigEntity = new ActivityWriteOffSourceConfigEntity();

        switch (activityType) {
            case 1:
                activityWriteOffSourceConfigEntity.setTemplateId(auditNodeTemplateId);
                activityWriteOffSourceConfigEntity.setCalculateType(WriteOffCalculateType.STORE_COST_SUM.value());
                activityWriteOffSourceConfigEntity.setApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
                activityWriteOffSourceConfigEntity.setRecordType("default__c");
                activityWriteOffSourceConfigEntity.setReferenceWriteOffFieldApiName(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
                activityWriteOffSourceConfigEntity.setDealerFieldApiName(TPMActivityProofAuditFields.DEALER_ID);
                activityWriteOffSourceConfigEntity.setReferenceActivityFieldApiName(TPMActivityProofAuditFields.ACTIVITY_ID);
                activityWriteOffSourceConfigEntity.setAccountFieldApiName(TPMActivityProofAuditFields.STORE_ID);
                activityWriteOffSourceConfigEntity.setCostFieldApiName(TPMActivityProofAuditFields.AUDIT_TOTAL);
                activityWriteOffSourceConfigEntity.setDisplayFieldApiNames(Lists.newArrayList(Lists.newArrayList(
                        TPMActivityProofAuditFields.PROOF_IMAGES,
                        TPMActivityProofAuditFields.AUDIT_STATUS,
                        TPMActivityProofAuditFields.ACTIVITY_PROOF_ID,
                        TPMActivityProofAuditFields.TOTAL,
                        TPMActivityProofAuditFields.AUDIT_TOTAL)));
                break;
            case 2:
                activityWriteOffSourceConfigEntity.setTemplateId(auditNodeTemplateId);
                activityWriteOffSourceConfigEntity.setCalculateType(WriteOffCalculateType.ACTIVITY.value());
                activityWriteOffSourceConfigEntity.setApiName("");
                activityWriteOffSourceConfigEntity.setRecordType("default__c");
                activityWriteOffSourceConfigEntity.setReferenceWriteOffFieldApiName("");
                activityWriteOffSourceConfigEntity.setDealerFieldApiName("");
                activityWriteOffSourceConfigEntity.setReferenceActivityFieldApiName(TPMActivityProofAuditFields.ACTIVITY_ID);
                activityWriteOffSourceConfigEntity.setAccountFieldApiName("");
                activityWriteOffSourceConfigEntity.setCostFieldApiName("");
                activityWriteOffSourceConfigEntity.setDisplayFieldApiNames(Lists.newArrayList());
                break;
            default:
                break;
        }

        ActivityWriteOffChargeUpConfigEntity activityWriteOffChargeUpConfigEntity = new ActivityWriteOffChargeUpConfigEntity();
        activityWriteOffChargeUpConfigEntity.setChargeUpAccountStatus(false);

        ActivityWriteOffConfigEntity activityWriteOffConfigEntity = new ActivityWriteOffConfigEntity();
        activityWriteOffConfigEntity.setWriteOffSourceConfig(activityWriteOffSourceConfigEntity);
        activityWriteOffConfigEntity.setChargeUpConfig(activityWriteOffChargeUpConfigEntity);
        activityWriteOffConfigEntity.setEnableOverWriteOff(false);

        activityNodeEntity.setActivityWriteOffConfig(activityWriteOffConfigEntity);
    }

    private void initAuditNode(String proofNodeTemplateId, ActivityNodeEntity activityNodeEntity) {
        ActivityProofAuditSourceConfigEntity activityProofAuditSourceConfigEntity = new ActivityProofAuditSourceConfigEntity();
        activityProofAuditSourceConfigEntity.setTemplateId(proofNodeTemplateId);
        activityProofAuditSourceConfigEntity.setUseComplexMode(true);
        activityProofAuditSourceConfigEntity.setMasterApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        activityProofAuditSourceConfigEntity.setMasterRecordType("default__c");
        activityProofAuditSourceConfigEntity.setReferenceAuditSourceFieldApiName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
        activityProofAuditSourceConfigEntity.setReferenceAuditSourceDetailFieldApiName(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_DETAIL_ID);
        activityProofAuditSourceConfigEntity.setReferenceActivityFieldApiName(TPMActivityProofAuditFields.ACTIVITY_ID);
        activityProofAuditSourceConfigEntity.setDetailApiName(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
        activityProofAuditSourceConfigEntity.setMasterDetailFieldApiName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
        activityProofAuditSourceConfigEntity.setDealerFieldApiName(TPMActivityProofAuditFields.DEALER_ID);
        activityProofAuditSourceConfigEntity.setAccountFieldApiName(TPMActivityProofAuditFields.STORE_ID);
        activityProofAuditSourceConfigEntity.setAgreementFieldApiName(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID);
        activityProofAuditSourceConfigEntity.setCostFieldApiName(TPMActivityProofFields.ACTUAL_TOTAL);
        activityProofAuditSourceConfigEntity.setAuditStatusApiName(TPMActivityProofFields.AUDIT_STATUS);

        activityProofAuditSourceConfigEntity.setDisplayFieldApiNamesOfMaster(
                Lists.newArrayList(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID,
                        TPMActivityProofFields.PROOF_IMAGES, TPMActivityProofFields.ACTUAL_TOTAL));

        activityProofAuditSourceConfigEntity.setDisplayFieldApiNamesOfDetail(
                Lists.newArrayList(TPMActivityProofDetailFields.PROOF_ITEM, TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD,
                        TPMActivityProofDetailFields.AMOUNT, TPMActivityProofDetailFields.SUBTOTAL));

        activityProofAuditSourceConfigEntity.setDisplayFieldApiNamesOfAuditMaster(
                Lists.newArrayList(TPMActivityProofAuditFields.AUDIT_IMAGES,
                        TPMActivityProofAuditFields.AUDIT_TOTAL, TPMActivityProofAuditFields.OPINION));

        activityProofAuditSourceConfigEntity.setDisplayFieldApiNamesOfAuditDetail(
                Lists.newArrayList(TPMActivityProofAuditDetailFields.AUDIT_AMOUNT, TPMActivityProofAuditDetailFields.AUDIT_SUBTOTAL));
        activityProofAuditSourceConfigEntity.setActivityItemFieldApiName("");

        ActivityProofAuditModeConfigEntity activityProofAuditModeConfigEntity = new ActivityProofAuditModeConfigEntity();
        activityProofAuditModeConfigEntity.setAuditMode(AuditModeType.ALL.value());

        ActivityProofAuditConfigEntity activityProofAuditConfigEntity = new ActivityProofAuditConfigEntity();
        activityProofAuditConfigEntity.setAuditSourceConfig(activityProofAuditSourceConfigEntity);
        activityProofAuditConfigEntity.setAuditModeConfig(activityProofAuditModeConfigEntity);

        activityNodeEntity.setActivityProofAuditConfig(activityProofAuditConfigEntity);
    }

    private void initProofNode(ActivityNodeEntity activityNodeEntity) {
        ActivityProofFrequencyConfigEntity activityProofFrequencyConfigEntity = new ActivityProofFrequencyConfigEntity();
        activityProofFrequencyConfigEntity.setFrequencyType(ProofFrequencyType.ACTIVITY.value());
        activityProofFrequencyConfigEntity.setFrequencyLimit("0");

        ActivityProofCostCalculateConfigEntity activityProofCostCalculateConfigEntity = new ActivityProofCostCalculateConfigEntity();
        activityProofCostCalculateConfigEntity.setCalculateType(ProofCalculateType.ACTIVITY.value());
        activityProofCostCalculateConfigEntity.setRatio("1");

        ActivityProofConfigEntity activityProofConfigEntity = new ActivityProofConfigEntity();
        activityProofConfigEntity.setFrequencyConfig(activityProofFrequencyConfigEntity);
        activityProofConfigEntity.setCostCalculateConfig(activityProofCostCalculateConfigEntity);

        activityNodeEntity.setActivityProofConfig(activityProofConfigEntity);
    }

    private String toSelectOptionsIdentity(List<ISelectOption> selectOptions) {
        return selectOptions
                .stream()
                .sorted(Comparator.comparing(ISelectOption::getValue))
                .map(m -> String.format("%s.%s", m.getValue(), m.getLabel()))
                .collect(Collectors.joining(","));
    }

    private String toActivityTypesIdentity(Map<String, String> activityTypeMap) {
        return activityTypeMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> String.format("%s.%s", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining(","));
    }

    private void nameValidation(String name) {
        if (Strings.isNullOrEmpty(name) ||
                name.length() < 2 ||
                name.length() > 200) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_NAME_FORMAT_ERROR));
        }
    }

    private void apiNameValidation(String apiName) {
        if (Strings.isNullOrEmpty(apiName) ||
                !apiName.endsWith("__c") ||
                apiName.length() < 7 ||
                apiName.length() > 32) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_API_NAME_FORMAT_ERROR));
        }
    }

    private void descriptionValidation(String description) {
        if (!Strings.isNullOrEmpty(description) && description.length() > 2000) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_DESCRIPTION_FORMAT_ERROR));
        }
    }

    private ActivityTypePO findActivityTypeByActivityId(String tenantId, String activityId) {
        IObjectData objectData;
        try {
            objectData = objectDataProxy.findById(activityId, tenantId, ApiNames.TPM_ACTIVITY_OBJ);
        } catch (MetadataServiceException e) {
            return null;
        }
        if (Objects.isNull(objectData)) {
            return null;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        String activityTypeId = objectDataExt.getStringValue(TPMActivityFields.ACTIVITY_TYPE);
        if (Strings.isNullOrEmpty(activityTypeId)) {
            return null;
        }
        return activityTypeDAO.get(tenantId, activityTypeId);
    }

    private ActivityTypePO findActivityTypeByActivityUnifiedCaseId(String tenantId, String activityUnifiedCaseId) {
        IObjectData objectData;
        try {
            objectData = objectDataProxy.findById(activityUnifiedCaseId, tenantId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        } catch (MetadataServiceException e) {
            return null;
        }
        if (Objects.isNull(objectData)) {
            return null;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        String activityTypeId = objectDataExt.getStringValue(TPMActivityUnifiedCaseFields.ACTIVITY_TYPE);
        if (Strings.isNullOrEmpty(activityTypeId)) {
            return null;
        }
        return activityTypeDAO.get(tenantId, activityTypeId);
    }

    private IActionContext getActionContext() {
        RequestContext requestContext = RequestContextManager.getContext();
        IActionContext actionContext = ActionContextExt.of(new User((String) null, (String) null)).getContext();
        boolean direct = false;
        boolean notNeedDeepCopy = false;
        Boolean upstreamCopyDescribe = null;
        if (Objects.nonNull(requestContext)) {
            if (Objects.nonNull(requestContext.getAttribute("direct"))) {
                direct = (Boolean) requestContext.getAttribute("direct");
            }

            if (Objects.nonNull(requestContext.getAttribute("not_need_deep_copy"))) {
                notNeedDeepCopy = (Boolean) requestContext.getAttribute("not_need_deep_copy");
            }

            if (Objects.nonNull(requestContext.getAttribute("upstream_copy_describe"))) {
                upstreamCopyDescribe = (Boolean) requestContext.getAttribute("upstream_copy_describe");
            }
        }

        actionContext.put("direct", direct);
        actionContext.put("not_need_deep_copy", notNeedDeepCopy);
        actionContext.put("upstream_copy_describe", upstreamCopyDescribe);
        actionContext.put("not_check_option_has_data", true);
        return actionContext;
    }
}
