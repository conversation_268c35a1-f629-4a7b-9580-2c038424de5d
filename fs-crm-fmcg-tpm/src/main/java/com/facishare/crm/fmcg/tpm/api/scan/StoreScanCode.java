package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.reward.dto.ActivityInformation;
import com.facishare.crm.fmcg.tpm.reward.dto.SnInformation;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 门店扫码接口
 * Author: Generated
 * Date: 2025-08-11
 */
public interface StoreScanCode {

    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * 门店ID
         */
        @JSONField(name = "store_id")
        @JsonProperty(value = "store_id")
        @SerializedName("store_id")
        private String storeId;

        /**
         * 互联企业ID
         */
        @JSONField(name = "interconnect_enterprise_id")
        @JsonProperty(value = "interconnect_enterprise_id")
        @SerializedName("interconnect_enterprise_id")
        private String interconnectEnterpriseId;

        /**
         * 互联用户ID
         */
        @JSONField(name = "interconnect_user_id")
        @JsonProperty(value = "interconnect_user_id")
        @SerializedName("interconnect_user_id")
        private String interconnectUserId;

        /**
         * 扫描的码
         */
        private String code;

        /**
         * 当前请求的环境,sandbox/dev/default
         */
        private String environment;

        /**
         * 租户编码
         */
        @JSONField(name = "tenant_code")
        @JsonProperty(value = "tenant_code")
        @SerializedName("tenant_code")
        private String tenantCode;

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        /**
         * 扫码状态
         */
        private String status;

        /**
         * 活动信息
         */
        @JSONField(name = "activity_information")
        @JsonProperty(value = "activity_information")
        @SerializedName("activity_information")
        private ActivityInformation activityInformation;

        /**
         * 条码信息
         */
        @JSONField(name = "sn_information")
        @JsonProperty(value = "sn_information")
        @SerializedName("sn_information")
        private SnInformation snInformation;

        /**
         * 状态信息描述
         */
        @JSONField(name = "status_message")
        @JsonProperty(value = "status_message")
        @SerializedName("status_message")
        private String statusMessage;
    }
}
