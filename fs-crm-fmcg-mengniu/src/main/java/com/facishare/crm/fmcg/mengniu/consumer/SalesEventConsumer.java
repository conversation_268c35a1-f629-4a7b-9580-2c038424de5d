package com.facishare.crm.fmcg.mengniu.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventAbandonException;
import com.facishare.crm.fmcg.mengniu.handler.SalesEventDistributor;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@SuppressWarnings("Duplicates")
@Component
public class SalesEventConsumer implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer processor;

    private static final String CONFIG_NAME = "gray-rel-fmcg";
    private static final String NAME_SERVER_KEY = "MN_SCAN_PAY_NAMESERVER";
    private static final String TOPIC_KEY = "MN_SCAN_PAY_TOPICS";
    private static final String GROUP_NAME_KEY = "MN_SCAN_PAY_CONSUMER_GROUP";
    private static final int MAX_RECONSUME_TIMES = 10;

    @Resource
    private SalesEventDistributor salesEventDistributor;

    @PostConstruct
    public void init() {
        String mqFlag = System.getProperty("mn.flag");
        if (!Objects.equals("1", mqFlag)) {
            return;
        }

        log.info("sales event consumer start init.");

        MessageListenerConcurrently listener = (messages, context) -> {
            log.info("message size : {}", messages.size());

            boolean reconsume = false;
            for (MessageExt messageExt : messages) {
                try {
                    // message.getReconsumeTimes() > 10 就不在处理了
                    if (messageExt.getReconsumeTimes() > MAX_RECONSUME_TIMES) {
                        log.warn("object-data reward process message reconsumeTimes > {}, no longer process, msgId: {}", MAX_RECONSUME_TIMES, messageExt.getMsgId());
                        continue;
                    }
                    process(messageExt);
                } catch (EventAbandonException ex) {
                    log.warn("sales event abandon : ", ex);
                } catch (Exception ex) {
                    log.error("sales event process error : ", ex);
                    reconsume = true;
                } finally {
                    TraceContext.remove();
                }
            }
            if (reconsume) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, listener);
            processor.setGroupNameKey(GROUP_NAME_KEY);
            processor.setConsumeTopicKey(TOPIC_KEY);
            processor.setNameServerKey(NAME_SERVER_KEY);

        } catch (Exception ex) {
            log.error("init sales event consumer error : ", ex);
        }
    }

    public void process(MessageExt messageExt) {
        TraceContext trace = TraceContext.get();
        if (Strings.isNullOrEmpty(trace.getTraceId())) {
            trace.setTraceId("SALES_EVENT." + messageExt.getMsgId());
        }

        String body = new String(messageExt.getBody());
        log.info("sales event received : {}", body);

        salesEventDistributor.process(JSON.parseObject(body));
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (processor != null && event.getApplicationContext().getParent() == null) {
            processor.start();
            log.info("sales event consumer started.");
        }
    }

    @PreDestroy
    public void shutDown() {
        if (processor != null) {
            processor.close();
            log.info("sales event consumer closed.");
        }
    }
}
